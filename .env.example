# ------------------<PERSON><PERSON><PERSON> tracing------------------
LANGCHAIN_PROJECT="default"
LANGCHAIN_API_KEY=""
LANGCHAIN_TRACING_V2=true
# -----------------------------------------------------

# At least one of these must be set. Defaults to Azure OpenAI models. Ensure this is consistent
# with what is expected for the default models that are set in the GraphConfigPydantic class in tools_agent/agent.py

# Azure OpenAI Configuration (primary)
AZURE_OPENAI_API_KEY="xxx"
AZURE_OPENAI_ENDPOINT="https://pep-aisp-hackathon.openai.azure.com/"
AZURE_OPENAI_API_VERSION="2023-05-15"
OPENAI_API_VERSION="2023-05-15"
AZURE_DEPLOYMENT_NAME_VISION="gpt-4o"
AZURE_DEPLOYMENT_NAME_COMPLETION="gpt-4o"
# Additional environment variables for LangChain compatibility
OPENAI_API_BASE="https://pep-aisp-hackathon.openai.azure.com/"
OPENAI_API_TYPE="azure"

# Standard OpenAI Configuration (fallback) - Using Azure key for compatibility
OPENAI_API_KEY="xxxx"
ANTHROPIC_API_KEY=""

# For user level authentication
SUPABASE_URL="https://avgmebrcliyhyrgqulmk.supabase.co"
# Ensure this is your Supabase Service Role key
SUPABASE_KEY="xxxx"

LANGSMITH_API_KEY="xxx"